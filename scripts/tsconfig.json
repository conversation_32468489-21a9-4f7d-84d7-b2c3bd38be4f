{
  "compilerOptions": {
    /* Language and Environment */
    "target": "ES2022",
    "lib": ["ES2022"],
    "module": "CommonJS",
    "moduleResolution": "node",
    
    /* Emit */
    "outDir": "./dist",
    "rootDir": "./",
    "declaration": false,
    "declarationMap": false,
    "sourceMap": true,
    "removeComments": false,
    "importHelpers": false,
    "downlevelIteration": true,
    "newLine": "lf",
    
    /* Interop Constraints */
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    
    /* Type Checking */
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "useUnknownInCatchVariables": true,
    "alwaysStrict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": false,
    
    /* Completeness */
    "skipLibCheck": true,
    
    /* Node.js specific */
    "types": ["node"],
    "resolveJsonModule": true,
    "allowJs": false,
    
    /* Path mapping for project imports */
    "baseUrl": "../",
    "paths": {
      "@/*": ["src/*"],
      "@/backend/*": ["src/backend/*"],
      "@/lib/*": ["src/lib/*"],
      "@/types/*": ["src/types/*"],
      "@/config/*": ["src/config/*"],
      "@/models/*": ["src/models/*"]
    }
  },
  "include": [
    "./**/*.ts",
    "./**/*.js",
    "../src/types/**/*.ts",
    "../src/config/**/*.ts",
    "../src/models/**/*.ts",
    "../src/backend/**/*.ts",
    "../src/lib/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "../node_modules",
    "../.next",
    "../out",
    "../build",
    "../coverage",
    "../e2e",
    "../public",
    "../docs",
    "../prisma/migrations",
    "**/*.test.ts",
    "**/*.spec.ts"
  ],
  "ts-node": {
    "esm": false,
    "experimentalSpecifierResolution": "node",
    "compilerOptions": {
      "module": "CommonJS",
      "target": "ES2022"
    }
  }
}
