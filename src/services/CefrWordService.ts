import { CefrWord } from '../models/CefrWord';
import { CefrWordRepository } from '../repositories/CefrWordRepository';

export class CefrWordService {
  constructor(private cefrWordRepository: CefrWordRepository) {}

  async getAllCefrWords(): Promise<CefrWord[]> {
    return this.cefrWordRepository.findAll();
  }

  async getCefrWordsByLevel(level: string): Promise<CefrWord[]> {
    return this.cefrWordRepository.findByLevel(level);
  }

  async getCefrWordById(id: string): Promise<CefrWord | null> {
    return this.cefrWordRepository.findById(id);
  }

  async createCefrWord(cefrWord: CefrWord): Promise<CefrWord> {
    return this.cefrWordRepository.create(cefrWord);
  }

  async updateCefrWord(id: string, cefrWord: CefrWord): Promise<CefrWord | null> {
    return this.cefrWordRepository.update(id, cefrWord);
  }

  async deleteCefrWord(id: string): Promise<void> {
    return this.cefrWordRepository.delete(id);
  }
}
